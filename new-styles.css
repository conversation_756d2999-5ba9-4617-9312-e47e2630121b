/* 4 Elements Hair - Premium Sleek Design */
/* Google Fonts moved to <link> tags in HTML for performance; remove @import */

/* Reset */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Prevent horizontal scrolling */
html,
body {
  overflow-x: hidden;
  width: 100%;
}

/* Improve mobile performance */
* {
  -webkit-tap-highlight-color: transparent;
}

/* Better font rendering */
body {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Optional initial fade only when .is-loading is present (toggled via JS), and respect prefers-reduced-motion */
/* removed initial fade to improve FCP/SI */

/* Focus visibility for keyboard users */
:focus-visible {
  outline: 3px solid var(--primary-pink);
  outline-offset: 2px;
}

:root {
  /* Premium Color Palette - Inspired by reference */
  --primary-pink: #f4c2c2;
  --soft-pink: #f9e5e5;
  --blush: #f7d7d7;
  --cream: #fdf9f7;
  --charcoal: #2c2c2c;
  --dark-grey: #4a4a4a;
  --light-grey: #8b8b8b;
  --white: #ffffff;
  --accent-gold: #d4af37;

  /* Typography */
  --font-primary: "Inter", sans-serif;
  --font-display: "Playfair Display", serif;

  /* Spacing */
  --section-padding: 80px 0;
  --container-max: 1200px;

  /* Transitions */
  --transition: all 0.3s ease;
}

html {
  scroll-behavior: smooth;
  scroll-padding-top: 100px; /* Ensures proper spacing when scrolling to anchors */
}

/* Mobile-specific scroll padding for the Ready to Book card */
@media (max-width: 768px) {
  html {
    scroll-padding-top: 150px; /* More padding on mobile to center the Ready to Book card */
  }
}

body {
  font-family: var(--font-primary), system-ui, -apple-system, Segoe UI, Roboto,
    Arial, sans-serif;
  font-size-adjust: 0.5;
  line-height: 1.6;
  color: var(--charcoal);
  background: var(--white);
  font-weight: 400;
}

.container {
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 20px;
}

/* Navigation */
.navbar {
  position: fixed;
  top: 0;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  z-index: 1000;
  padding: 15px 0;
  transition: var(--transition);
  border-bottom: 1px solid rgba(244, 194, 194, 0.1);
}

/* Scrolled navbar state to avoid style writes in JS */
.navbar.scrolled {
  background: rgba(255, 255, 255, 0.98);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: var(--container-max);
  margin: 0 auto;
  padding: 0 20px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo a {
  text-decoration: none;
  display: flex;
  align-items: center;
}

.logo img {
  height: 100px;
  width: auto;
  object-fit: contain;
  max-width: 300px;
  transition: opacity 0.3s ease;
}

.logo a:hover img {
  opacity: 0.8;
}

/* Scroll-animated elements use class-based states only (avoid shifting items already in viewport) */
/* initial states moved to .pre-animate; see class rules below */

/* Class-driven animations to avoid JS style writes */
.pre-animate {
  opacity: 0;
}
.anim-fade-up {
  transform: translateY(30px);
}
.anim-fade-left {
  transform: translateX(-30px);
}
.anim-fade-right {
  transform: translateX(30px);
}
.reveal {
  opacity: 1;
  transform: none;
  transition: opacity 0.6s ease, transform 0.6s ease;
}

.logo-text {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  display: none; /* Hide text since logo includes text */
}

.nav-menu {
  display: flex;
  align-items: center;
  gap: 40px;
}

.nav-link {
  text-decoration: none;
  color: var(--charcoal);
  font-weight: 500;
  font-size: 15px;
  transition: var(--transition);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-pink);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -5px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-pink);
  transition: var(--transition);
}

.nav-link:hover::after {
  width: 100%;
}

.book-btn {
  background: var(--primary-pink);
  color: var(--white);
  padding: 12px 24px;
  border-radius: 6px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: var(--transition);
}

.book-btn:hover {
  background: var(--charcoal);
  transform: translateY(-2px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 8px;
  gap: 4px;
}

.mobile-menu-toggle span {
  width: 25px;
  height: 3px;
  background: var(--charcoal);
  transition: var(--transition);
  border-radius: 2px;
}

.mobile-menu-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.mobile-menu-toggle.active span:nth-child(2) {
  opacity: 0;
}

.mobile-menu-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* Mobile Navigation Menu */
.nav-menu.mobile-active {
  display: flex;
  position: fixed;
  top: 130px; /* Match navbar height */
  left: 0;
  right: 0;
  background: var(--white);
  flex-direction: column;
  padding: 15px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
  border-top: 1px solid rgba(244, 194, 194, 0.3);
  backdrop-filter: blur(10px);
  z-index: 999;
  animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-menu.mobile-active .nav-link,
.nav-menu.mobile-active .book-btn {
  margin-bottom: 4px;
  text-align: center;
  padding: 12px 20px;
  border-radius: 8px;
  transition: var(--transition);
  font-weight: 500;
  font-size: 16px;
}

.nav-menu.mobile-active .nav-link {
  color: var(--charcoal);
  border: 2px solid transparent;
}

.nav-menu.mobile-active .nav-link:hover,
.nav-menu.mobile-active .nav-link:focus {
  background: var(--soft-pink);
  border-color: var(--primary-pink);
  transform: translateY(-2px);
}

.nav-menu.mobile-active .book-btn {
  margin-bottom: 0;
  background: var(--primary-pink);
  color: var(--white);
  font-weight: 600;
  box-shadow: 0 4px 15px rgba(244, 194, 194, 0.4);
}

.nav-menu.mobile-active .book-btn:hover {
  background: var(--charcoal);
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(44, 44, 44, 0.3);
}

/* Mobile menu overlay */
.mobile-menu-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  z-index: 998;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.mobile-menu-overlay.active {
  opacity: 1;
  visibility: visible;
}

/* Prevent body scroll when mobile menu is open */
body.mobile-menu-open {
  overflow: hidden;
}

/* Hero Section - Premium Redesign */
.hero {
  min-height: 100svh;
  min-height: 100vh; /* fallback */
  background: linear-gradient(135deg, #fdf9f7 0%, #f9e5e5 50%, #f4c2c2 100%);
  display: flex;
  align-items: center;
  position: relative;
  overflow: hidden;
  padding-top: 130px; /* Account for fixed navbar */
}

.hero::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="0.5" fill="%23F4C2C2" opacity="0.1"/><circle cx="80" cy="40" r="0.3" fill="%23F4C2C2" opacity="0.1"/><circle cx="40" cy="80" r="0.4" fill="%23F4C2C2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  z-index: 1;
}

.hero-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 100px;
  align-items: center;
  width: 100%;
  position: relative;
  z-index: 2;
}

.hero-text {
  position: relative;
}

.hero-text::before {
  content: "";
  position: absolute;
  top: -40px;
  left: -20px;
  width: 120px;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-pink), var(--accent-gold));
  border-radius: 2px;
}

.hero-text h1 {
  font-family: var(--font-display);
  font-size: 72px;
  font-weight: 700;
  line-height: 1.05;
  color: var(--charcoal);
  margin-bottom: 32px;
  letter-spacing: -0.02em;
}

.hero-text .highlight {
  background: linear-gradient(135deg, var(--primary-pink), #e8a5a5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-style: italic;
  position: relative;
}

.hero-text .highlight::after {
  content: "";
  position: absolute;
  bottom: 8px;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--primary-pink), transparent);
  opacity: 0.3;
}

.hero-subtitle {
  font-size: 22px;
  color: var(--dark-grey);
  margin-bottom: 16px;
  font-weight: 500;
  letter-spacing: 0.5px;
  text-transform: uppercase;
  font-size: 14px;
}

.hero-text p {
  font-size: 20px;
  color: var(--dark-grey);
  margin-bottom: 48px;
  line-height: 1.7;
  max-width: 520px;
  font-weight: 400;
}

.hero-stats {
  display: flex;
  gap: 40px;
  margin-bottom: 48px;
}

.stat-item {
  text-align: left;
}

.stat-number {
  font-family: var(--font-display);
  font-size: 36px;
  font-weight: 700;
  color: var(--primary-pink);
  display: block;
  line-height: 1;
}

.stat-label {
  font-size: 14px;
  color: var(--light-grey);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-top: 4px;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  align-items: center;
}

.btn-primary {
  background: var(--charcoal);
  color: var(--white);
  padding: 18px 36px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: var(--transition);
  border: 2px solid var(--charcoal);
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  background: var(--primary-pink);
  border-color: var(--primary-pink);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(244, 194, 194, 0.4);
}

.btn-secondary {
  background: transparent;
  color: var(--charcoal);
  padding: 18px 36px;
  border-radius: 8px;
  text-decoration: none;
  font-weight: 600;
  font-size: 16px;
  transition: var(--transition);
  border: 2px solid var(--charcoal);
  position: relative;
}

.btn-secondary:hover {
  background: var(--charcoal);
  color: var(--white);
  transform: translateY(-2px);
}

/* Secondary button on dark backgrounds */
.cta-section .btn-secondary {
  background: transparent;
  color: var(--white);
  border: 2px solid var(--white);
}

.cta-section .btn-secondary:hover {
  background: var(--white);
  color: var(--charcoal);
  transform: translateY(-2px);
}

.hero-visual {
  position: relative;
  height: 700px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-image-main {
  position: relative;
  width: 100%;
  height: 600px;
  background: linear-gradient(135deg, var(--primary-pink), #e8a5a5);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 30px 60px rgba(244, 194, 194, 0.3);
  transform: rotate(-2deg);
  transition: var(--transition);
}

.hero-image-main:hover {
  transform: rotate(0deg) scale(1.02);
}

.hero-image-main img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 20px;
}

.hero-image-main::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 50%,
    rgba(255, 255, 255, 0.05) 100%
  );
}

.hero-floating-elements {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.floating-element {
  position: absolute;
  background: var(--white);
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.floating-element-1 {
  top: 10%;
  right: -10%;
  width: 180px;
  animation: float 6s ease-in-out infinite;
}

.floating-element-2 {
  bottom: 15%;
  left: -15%;
  width: 160px;
  animation: float 8s ease-in-out infinite reverse;
}

.floating-element h4 {
  font-family: var(--font-display);
  font-size: 18px;
  color: var(--charcoal);
  margin-bottom: 8px;
}

.floating-element p {
  font-size: 14px;
  color: var(--light-grey);
  margin: 0;
}

.hero-image-placeholder {
  color: var(--white);
  text-align: center;
  z-index: 2;
  position: relative;
}

.hero-image-placeholder i {
  font-size: 100px;
  margin-bottom: 24px;
  opacity: 0.9;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.1));
}

.hero-image-placeholder p {
  font-size: 20px;
  font-weight: 600;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

/* Elements Section */
.elements-section {
  padding: var(--section-padding);
  background: var(--white);
}

.section-header {
  text-align: center;
  margin-bottom: 80px;
}

.section-title {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.section-subtitle {
  font-size: 18px;
  color: var(--light-grey);
  max-width: 600px;
  margin: 0 auto;
}

.elements-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
}

/* Single row layout for The Seven Elements on desktop */
.elements-grid.pyramid {
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 24px;
}

/* Desktop: all 7 elements in one row */
.elements-grid.pyramid .element-card {
  padding: 32px 20px;
}

/* Tablet: 4-3 layout */
@media (max-width: 1024px) {
  .elements-grid.pyramid {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
  }
  .elements-grid.pyramid .element-card:nth-child(5) {
    grid-column: 1;
  }
  .elements-grid.pyramid .element-card:nth-child(6) {
    grid-column: 2;
  }
  .elements-grid.pyramid .element-card:nth-child(7) {
    grid-column: 3;
  }
}

/* Mobile: 2-2-2-1 descending layout */
@media (max-width: 768px) {
  .elements-grid.pyramid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
  }
  .elements-grid.pyramid .element-card {
    grid-column: auto;
  }
  /* Last element (Energy) spans full width */
  .elements-grid.pyramid .element-card:nth-child(7) {
    grid-column: 1 / -1;
  }
}

.element-card {
  text-align: center;
  padding: 40px 20px;
  border-radius: 12px;
  background: var(--white);
  border: 1px solid rgba(244, 194, 194, 0.2);
  transition: var(--transition);
}

.element-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}

.element-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  transition: var(--transition);
}

.element-card:hover .element-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}

.element-icon svg {
  width: 32px;
  height: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}

.element-card:hover .element-icon svg {
  color: var(--white);
}

.element-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.element-card p {
  color: var(--light-grey);
  font-size: 15px;
  line-height: 1.6;
}

/* Services Section */
.services-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.services-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.services-text h2 {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
  line-height: 1.2;
}

.services-text p {
  font-size: 18px;
  color: var(--dark-grey);
  margin-bottom: 32px;
  line-height: 1.7;
}

.services-list {
  list-style: none;
  margin-bottom: 40px;
}

.services-list li {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
  font-size: 16px;
  color: var(--charcoal);
}

.services-list svg {
  color: var(--primary-pink);
  margin-right: 12px;
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.services-image {
  height: 500px;
  background: var(--white);
  border-radius: 12px;
  position: relative; /* anchor absolutely positioned media */
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

/* Ensure picture fills the holder so the image covers fully */
.services-image picture {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: block;
  width: 100%;
  height: 100%;
}

.services-image img {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

/* Services Grid */
.services-grid-section {
  padding: var(--section-padding);
  background: var(--white);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

/* Service card link wrapper */
.service-card-link {
  text-decoration: none;
  color: inherit;
  display: block;
  transition: var(--transition);
}

.service-card-link:hover {
  text-decoration: none;
  color: inherit;
}

.service-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
  position: relative;
  overflow: hidden;
  cursor: pointer;
  display: flex;
  flex-direction: column;
}

.service-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-pink), var(--accent-gold));
  transform: scaleX(0);
  transition: var(--transition);
}

.service-card:hover::before,
.service-card-link:hover .service-card::before {
  transform: scaleX(1);
}

.service-card:hover,
.service-card-link:hover .service-card {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}

.service-card.featured {
  border: 2px solid var(--primary-pink);
  background: linear-gradient(135deg, var(--white) 0%, var(--soft-pink) 100%);
}

.service-card.featured::before {
  transform: scaleX(1);
}

.service-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24px;
  transition: var(--transition);
}

.service-card:hover .service-icon,
.service-card-link:hover .service-card .service-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}

.service-card.featured .service-icon {
  background: var(--primary-pink);
}

.service-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}

.service-card:hover .service-icon i,
.service-card-link:hover .service-card .service-icon i,
.service-card.featured .service-icon i {
  color: var(--white);
}

.service-card h3 {
  font-family: var(--font-display);
  font-size: 28px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.service-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.service-features {
  list-style: none;
  margin-bottom: 24px;
}

.service-features li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  color: var(--charcoal);
}

.service-features i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 16px;
}

.service-price {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-pink);
  text-align: right;
}

/* Process Section */
.process-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.process-step {
  text-align: center;
  position: relative;
}

.step-number {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-family: var(--font-display);
  font-size: 32px;
  font-weight: 700;
  color: var(--white);
  box-shadow: 0 8px 20px rgba(244, 194, 194, 0.4);
}

.process-step h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.process-step p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: var(--section-padding);
  background: var(--charcoal);
  text-align: center;
}

.cta-content h2 {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

/* Page Header */
.page-header {
  padding: 140px 0 80px;
  background: linear-gradient(135deg, var(--soft-pink) 0%, var(--cream) 100%);
  text-align: center;
  position: relative;
}

.page-header::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="20" cy="20" r="0.5" fill="%23F4C2C2" opacity="0.1"/><circle cx="80" cy="40" r="0.3" fill="%23F4C2C2" opacity="0.1"/><circle cx="40" cy="80" r="0.4" fill="%23F4C2C2" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.page-header-content {
  position: relative;
  z-index: 2;
}

.page-header h1 {
  font-family: var(--font-display), Georgia, "Times New Roman", serif;
  font-size: 56px;
  font-weight: 700;
  color: var(--charcoal);
  margin-bottom: 20px;
  letter-spacing: -0.02em;
  font-size-adjust: 0.46;
}

.page-header .highlight {
  background: linear-gradient(135deg, var(--primary-pink), #e8a5a5);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-style: italic;
}

.page-header p {
  font-size: 20px;
  color: var(--dark-grey);
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* Services Grid */
.services-grid-section {
  padding: var(--section-padding);
  background: var(--white);
}

.services-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
}

.service-card h3 {
  font-family: var(--font-display);
  font-size: 28px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.service-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.service-features {
  list-style: none;
  margin-bottom: 24px;
}

.service-features li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  color: var(--charcoal);
}

.service-features i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 16px;
}

.service-price {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 700;
  color: var(--primary-pink);
  text-align: right;
}

/* Process Section */
.process-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.process-steps {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.process-step {
  text-align: center;
  position: relative;
}

.step-number {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  font-family: var(--font-display);
  font-size: 32px;
  font-weight: 700;
  color: var(--white);
  box-shadow: 0 8px 20px rgba(244, 194, 194, 0.4);
}

.process-step h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.process-step p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

/* CTA Section */
.cta-section {
  padding: var(--section-padding);
  background: var(--charcoal);
  text-align: center;
}

.cta-content h2 {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 600;
  color: var(--white);
  margin-bottom: 20px;
}

.cta-content p {
  font-size: 20px;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 40px;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

/* Active Navigation State */
.nav-link.active {
  color: var(--primary-pink);
}

.nav-link.active::after {
  width: 100%;
}

/* About Page Styles */
.about-hero {
  padding: var(--section-padding);
  background: var(--white);
}

.about-content {
  display: grid;
  grid-template-columns: 1fr 1.2fr;
  gap: 80px;
  align-items: center;
}

.about-image {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.3);
  transition: var(--transition);
  height: 500px;
  width: 100%;
}

.about-image:hover {
  transform: translateY(-8px);
  box-shadow: 0 30px 70px rgba(244, 194, 194, 0.4);
}

.about-image img {
  width: 100%;
  height: 500px;
  object-fit: cover;
  object-position: center;
  display: block;
}

.about-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.about-text p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 24px;
}

.credentials {
  margin-top: 40px;
}

.credential-item {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 24px;
  padding: 20px;
  background: var(--soft-pink);
  border-radius: 12px;
  transition: var(--transition);
}

.credential-item:hover {
  transform: translateX(8px);
  box-shadow: 0 8px 25px rgba(244, 194, 194, 0.2);
}

.credential-icon {
  width: 60px;
  height: 60px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.credential-icon i {
  font-size: 24px;
  color: var(--white);
}

.credential-text h4 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 4px;
}

.credential-text p {
  font-size: 14px;
  color: var(--light-grey);
  margin: 0;
}

.experience-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.experience-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.experience-card {
  text-align: center;
  padding: 40px 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}

.experience-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}

.experience-number {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 700;
  color: var(--primary-pink);
  display: block;
  line-height: 1;
  margin-bottom: 16px;
}

.experience-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 12px;
}

.experience-card p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

.philosophy-section {
  padding: var(--section-padding);
  background: var(--white);
}

.philosophy-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 80px;
  align-items: center;
}

.philosophy-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.philosophy-text > p {
  font-size: 20px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 40px;
  font-style: italic;
  position: relative;
  padding-left: 20px;
}

.philosophy-text > p::before {
  content: '"';
  position: absolute;
  left: 0;
  top: -10px;
  font-size: 60px;
  color: var(--primary-pink);
  font-family: var(--font-display);
}

.philosophy-points {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.philosophy-point {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.point-icon svg {
  width: 18px;
  height: 18px;
  color: var(--primary-pink);
  margin-top: 4px;
}

.philosophy-point h4 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 8px;
}

.philosophy-point p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
}

.philosophy-image {
  height: 500px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.3);
}

.philosophy-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

.specialties-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.specialties-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

/* Redesigned Philosophy (Hair Health & Beauty) — desktop-first */
#philosophy .philosophy-hero {
  display: grid;
  grid-template-columns: 1.05fr 1fr; /* image left, content right */
  gap: 80px;
  align-items: center;
}

.philosophy-hero-image {
  position: relative;
  height: 520px;
  border-radius: 18px;
  overflow: hidden;
  box-shadow: 0 24px 60px rgba(244, 194, 194, 0.35);
}

.philosophy-hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
}

.philosophy-hero-content {
  padding: 16px 0;
}

.philosophy-hero-content h3 {
  font-family: var(--font-display);
  font-size: 40px;
  font-weight: 600;
  color: var(--charcoal);
  margin: 0 0 16px 0;
}

.philosophy-hero-content p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.8;
  margin-bottom: 18px;
}

/* Premium feature list */
.feature-list {
  list-style: none;
  padding: 0;
  margin: 24px 0 0 0;
  display: grid;
  grid-template-columns: 1fr;
  gap: 12px;
}

.feature-list li {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
  color: var(--charcoal);
}

.feature-list i {
  color: var(--primary-pink);
  font-size: 18px;
}

/* Responsiveness for redesigned philosophy hero */
@media (max-width: 1024px) {
  #philosophy .philosophy-hero {
    gap: 40px;
  }
  .philosophy-hero-image {
    height: 460px;
  }
}

@media (max-width: 768px) {
  #philosophy .philosophy-hero {
    grid-template-columns: 1fr;
    gap: 24px;
    text-align: center;
  }
  .philosophy-hero-image {
    height: 360px;
    order: 1;
  }
  .philosophy-hero-content {
    order: 2;
  }
  .feature-list {
    text-align: left;
    max-width: 720px;
    margin-left: auto;
    margin-right: auto;
  }
}

/* Center My Philosophy heading and the quote below it */
#philosophy .philosophy-text h2 {
  text-align: center;
  margin-left: auto;
  margin-right: auto;
}
#philosophy .philosophy-text blockquote {
  text-align: center;
  margin: 0 auto 32px;
  max-width: 820px;
  color: var(--dark-grey);
  font-style: italic;
  line-height: 1.8;
}

.specialty-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}

.specialty-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}

.specialty-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
}

.specialty-icon i {
  font-size: 32px;
  color: var(--white);
}

.specialty-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.specialty-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
}

/* Sustainability Page Styles */
.sustainability-stats {
  padding: var(--section-padding);
  background: var(--white);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.stat-card {
  text-align: center;
  padding: 40px 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
}

.stat-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}

.stat-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  transition: var(--transition);
}

.stat-card:hover .stat-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}

.stat-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}

.stat-card:hover .stat-icon i {
  color: var(--white);
}

.stat-number {
  font-family: var(--font-display);
  font-size: 48px;
  font-weight: 700;
  color: var(--primary-pink);
  display: block;
  line-height: 1;
  margin-bottom: 8px;
}

.stat-label {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.stat-card p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

.sustainable-practices {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.practices-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 40px;
  margin-top: 60px;
}

.practice-card {
  background: var(--white);
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}

/* Center all inner text in Sustainable Practices cards */
.practice-card {
  text-align: center;
}
.practice-features {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0;
  margin: 0 auto;
}
.practice-features li {
  justify-content: center;
}
.practice-features i {
  margin-right: 8px;
}

.practice-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}

.practice-icon {
  width: 80px;
  height: 80px;
  background: var(--primary-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px; /* center the icon circle */
}

.practice-icon i {
  font-size: 32px;
  color: var(--white);
}

.practice-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.practice-card p {
  color: var(--dark-grey);
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 24px;
}

.practice-features {
  list-style: none;
}

.practice-features li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 15px;
  color: var(--charcoal);
}

.practice-features i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 16px;
}

.ponytail-donation {
  padding: var(--section-padding);
  background: var(--white);
}

.donation-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.donation-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.donation-text p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 32px;
}

.donation-requirements h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 20px;
}

.donation-requirements ul {
  list-style: none;
  margin-bottom: 32px;
}

.donation-requirements li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--charcoal);
}

.donation-requirements i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 18px;
}

.donation-image {
  height: 400px;
  background: var(--soft-pink);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.donation-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}

.partnership-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.partnership-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
}

.partnership-image {
  height: 400px;
  background: var(--white);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.partnership-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}

.partnership-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.partnership-text p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 32px;
}

.partnership-benefits h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 20px;
}

.partnership-benefits ul {
  list-style: none;
  margin-bottom: 32px;
}

.partnership-benefits li {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 16px;
  color: var(--charcoal);
}

.partnership-benefits i {
  color: var(--primary-pink);
  margin-right: 12px;
  font-size: 18px;
}

/* Contact Page Styles */
.contact-info {
  padding: var(--section-padding);
  background: var(--white);
}

.contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
}

.contact-card {
  text-align: center;
  padding: 40px 20px;
  background: var(--white);
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(244, 194, 194, 0.1);
  transition: var(--transition);
}

.contact-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
  border-color: var(--primary-pink);
}

.contact-icon {
  width: 80px;
  height: 80px;
  background: var(--soft-pink);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 24px;
  transition: var(--transition);
}

.contact-card:hover .contact-icon {
  background: var(--primary-pink);
  transform: scale(1.1);
}

.contact-icon i {
  font-size: 32px;
  color: var(--charcoal);
  transition: var(--transition);
}

.contact-card:hover .contact-icon i {
  color: var(--white);
}

.contact-card h3 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 12px;
}

.contact-card p {
  font-size: 16px;
  color: var(--charcoal);
  margin-bottom: 8px;
  line-height: 1.5;
}

.contact-card p a {
  color: var(--primary-pink);
  text-decoration: none;
  font-weight: 600;
}

.contact-card p a:hover {
  text-decoration: underline;
}

.contact-card span {
  font-size: 14px;
  color: var(--light-grey);
}

.booking-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.booking-content {
  display: grid;
  grid-template-columns: 1.2fr 1fr;
  gap: 80px;
  align-items: center;
}

/* When booking image is moved inside text, use a single column layout */
.booking-content.single-col {
  grid-template-columns: 1fr;
}

/* Spacing and centering for inline booking image */
.booking-text .booking-image {
  margin: 20px auto 30px;
  max-width: 560px;
}

.booking-text h2 {
  font-family: var(--font-display);
  font-size: 42px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 24px;
}

.booking-text > p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 40px;
}

.booking-benefits {
  margin-bottom: 40px;
}

.booking-benefit {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  margin-bottom: 24px;
}

.booking-benefit i {
  font-size: 24px;
  color: var(--primary-pink);
  margin-top: 4px;
}

.booking-benefit h4 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 8px;
}

.booking-benefit p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
  margin: 0;
}

.booking-cta {
  background: var(--white);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
}

.booking-cta h3 {
  font-family: var(--font-display);
  font-size: 28px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 12px;
}

.booking-cta p {
  font-size: 16px;
  color: var(--dark-grey);
  margin-bottom: 24px;
}

.booking-cta a {
  color: var(--primary-pink);
  text-decoration: none;
  font-weight: 600;
}

.booking-buttons {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.booking-image {
  height: 500px;
  background: var(--white);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.booking-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}

.location-section {
  padding: var(--section-padding);
  background: var(--white);
}

.location-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 80px;
  align-items: center;
  margin-top: 60px;
}

.location-info h3 {
  font-family: var(--font-display);
  font-size: 32px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.location-info > p {
  font-size: 18px;
  color: var(--dark-grey);
  line-height: 1.7;
  margin-bottom: 32px;
}

.location-features {
  margin-bottom: 32px;
}

.location-feature {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.location-feature i {
  font-size: 20px;
  color: var(--primary-pink);
}

.location-feature span {
  font-size: 16px;
  color: var(--charcoal);
}

.directions h4 {
  font-family: var(--font-display);
  font-size: 24px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.directions p {
  font-size: 15px;
  color: var(--dark-grey);
  margin-bottom: 8px;
}

.location-map {
  height: 400px;
  background: var(--soft-pink);
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.location-map img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  display: block;
  border-radius: 16px;
}

.faq-section {
  padding: var(--section-padding);
  background: var(--soft-pink);
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  margin-top: 60px;
}

.faq-item {
  background: var(--white);
  padding: 32px;
  border-radius: 16px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.08);
  transition: var(--transition);
}

.faq-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 50px rgba(244, 194, 194, 0.2);
}

.faq-item h3 {
  font-family: var(--font-display);
  font-size: 20px;
  font-weight: 600;
  color: var(--charcoal);
  margin-bottom: 16px;
}

.faq-item p {
  color: var(--dark-grey);
  font-size: 15px;
  line-height: 1.6;
}

/* Footer */
.footer {
  background: var(--charcoal);
  color: var(--white);
  padding: 60px 0 30px;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 60px;
  margin-bottom: 40px;
}

.footer-section h3 {
  font-family: var(--font-display);
  font-size: 24px;
  margin-bottom: 20px;
  color: var(--primary-pink);
}

.footer-section p,
.footer-section a {
  color: rgba(255, 255, 255, 0.8);
  text-decoration: none;
  margin-bottom: 8px;
  display: block;
  transition: var(--transition);
}

.footer-section a:hover {
  color: var(--primary-pink);
}

.footer-bottom {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 30px;
  text-align: center;
  color: rgba(255, 255, 255, 0.6);
}

/* Utility visibility classes (default: desktop) */
.desktop-only {
  display: block;
}
.mobile-only {
  display: none;
}

/* Responsive */
@media (max-width: 1024px) {
  .hero-content {
    gap: 60px;
  }

  .hero-text h1 {
    font-size: 64px;
  }

  .floating-element-1,
  .floating-element-2 {
    display: none;
  }

  .elements-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .services-content {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }

  /* Tablet: stack location section vertically */
  .location-content {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }
  .location-info {
    order: 1;
  }
  .location-map {
    order: 2;
    height: 360px;
  }

  .about-content {
    gap: 60px;
  }

  .container {
    padding: 0 30px;
  }
}

@media (max-width: 768px) {
  .hero {
    padding-top: 110px; /* Adjust for smaller mobile navbar */
  }

  .hero-content {
    grid-template-columns: 1fr;
    gap: 50px;
    text-align: center;
  }

  .hero-text::before {
    left: 50%;
    transform: translateX(-50%);
  }

  .hero-text h1 {
    font-size: 52px;
  }

  .hero-stats {
    justify-content: center;
  }

  .hero-visual {
    height: 500px;
  }

  .hero-image-main {
    height: 450px;
    transform: rotate(0deg);
  }

  /* Mobile dropdown positioning */
  .nav-menu.mobile-active {
    top: 110px; /* Match mobile navbar height */
  }

  /* Page header responsive padding */
  .page-header {
    padding: 120px 0 60px;
  }

  .elements-grid {
    grid-template-columns: 1fr 1fr;
    gap: 20px;
  }

  .services-content {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 40px;
    text-align: center;
  }

  /* Mobile: place section images between description and lists */
  /* 1) Services: Specializing in Blonde & Color Correction */
  #services .services-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  #services .services-text {
    display: contents;
  }
  #services .services-text h2 {
    order: 1;
  }
  #services .services-text > p {
    order: 2;
  }
  #services .services-image {
    order: 3;
    height: 300px;
    align-self: center;
    margin: 0 auto;
  }
  #services .services-list {
    order: 4;
  }
  #services .btn-primary {
    order: 5;
    align-self: center;
  }

  /* 2) About: Meet Vic */
  #about .services-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  #about .services-text {
    display: contents;
  }
  #about .services-text h2 {
    order: 1;
  }
  #about .services-text > p {
    order: 2;
  }
  #about .services-image {
    order: 3;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  #about .services-list {
    order: 4;
  }
  #about .btn-primary {
    order: 5;
    align-self: center;
  }

  /* 2b) Philosophy: place Vic's photo between the intro description and the key points on mobile */
  #philosophy .services-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  /* Flatten the text wrapper so we can reorder its children */
  #philosophy .services-text {
    display: contents;
  }
  /* Keep both intro paragraphs before the image */
  #philosophy .services-text > p {
    order: 1;
  }
  /* Center the image and place it between the intro and key points */
  #philosophy .services-image {
    order: 3;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  /* Ensure the key points list comes after the image */
  #philosophy .services-list {
    order: 4;
  }

  /* 3) Sustainability */
  #sustainability .services-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  #sustainability .services-text {
    display: contents;
  }
  #sustainability .services-text h2 {
    order: 1;
  }
  #sustainability .services-text > p {
    order: 2;
  }
  #sustainability .services-image {
    order: 3;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  #sustainability .services-list {
    order: 4;
  }
  #sustainability .btn-primary {
    order: 5;
    align-self: center;
  }

  /* 4) Contact: show image between Visit Our Salon header and Get In Touch */
  #contact .services-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
  #contact .services-image {
    order: 1; /* image first inside the content block */
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  #contact .services-text {
    order: 2;
  }

  /* Fix mobile layout for contact/visit section */
  .services-text {
    order: 1;
    width: 100%;
  }

  .services-image {
    order: 2;
    height: 300px;
    width: 100%;
  }

  .services-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 12px;
  }

  /* Mobile: stack My Philosophy section vertically */
  .philosophy-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }

  /* Slightly reduce image height on mobile for better fit */
  .philosophy-image {
    height: 360px;
  }

  .philosophy-image img {
    height: 100%;
  }

  /* Mobile order: text above, image below */
  .philosophy-text {
    order: 1;
  }
  .philosophy-image {
    order: 2;
  }

  /* Mobile: place image between the intro and the points */
  .philosophy-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
  }
  .philosophy-text {
    display: contents;
  }
  .philosophy-text h2 {
    order: 1;
  }
  .philosophy-text > p {
    order: 2;
  }
  .philosophy-image {
    order: 3;
  }
  .philosophy-points {
    order: 4;
  }

  /* Mobile: show icons above each point's text */
  .philosophy-point {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 8px;
  }
  .point-icon svg {
    margin: 0 0 6px 0;
  }

  /* Sustainability page: stack Ponytail Donation section on mobile
     Place the image between the description and the requirements */
  .donation-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
    text-align: center;
  }
  .donation-text {
    display: contents;
  }
  .donation-text h2 {
    order: 1;
  }
  .donation-text p {
    order: 2;
  }
  .donation-image {
    order: 3;
    height: 320px;
    align-self: center;
    margin: 0 auto;
  }
  .donation-requirements {
    order: 4;
  }
  /* Center Donation Requirements list items on mobile */
  .donation-requirements ul {
    text-align: center;
    padding-left: 0; /* remove default list padding on mobile */
    margin-left: auto;
    margin-right: auto;
  }
  .donation-requirements li {
    justify-content: center;
  }

  .donation-content .btn-primary {
    order: 5;
    align-self: center;
  }

  /* Ensure location section stacks on mobile */
  .location-content {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 40px;
    text-align: center;
    margin-top: 24px; /* place image closer to the section description */
  }
  /* Mobile ordering for Contact location: image, then info (Easy to Find + features), then Getting Here */
  .location-map {
    order: 1 !important;
    height: 320px;
  }

  /* Sustainability page: stack Partnership section on mobile
     Place the image between the description and the recycled list */
  .partnership-content {
    display: flex;
    flex-direction: column;
    gap: 40px;
    text-align: center;
  }
  /* Flatten inner text container so its children can be re-ordered */
  .partnership-text {
    display: contents;
  }
  .partnership-text h2 {
    order: 1;
  }
  .partnership-text > p {
    order: 2;
  }
  .partnership-image {
    order: 3;
    height: 320px;
  }
  .partnership-benefits {
    order: 4;
  }
  .partnership-text .btn-secondary {
    order: 5;
    align-self: center;
  }

  .location-info {
    order: 2 !important; /* keep content stacking inside */
    display: block; /* ensure it's a single grid item, not flattened */
  }
  /* Reset any previous ordering attempts */
  .directions {
    order: initial;
  }
  /* Center the feature list rows on mobile */
  .location-features {
    margin-left: auto;
    margin-right: auto;
    text-align: center;
  }
  .location-feature {
    justify-content: center;
  }

  /* Ensure proper mobile section spacing */
  .elements-section {
    padding: 60px 0;
  }

  .section-header {
    margin-bottom: 40px;
  }

  .section-title {
    font-size: 32px;
    margin-bottom: 16px;
  }

  .section-subtitle {
    font-size: 16px;
    line-height: 1.5;
  }

  .about-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .about-image {
    height: 400px;
  }

  .faq-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  /* Mobile booking section */
  .booking-content {
    grid-template-columns: 1fr;
    gap: 40px;
    text-align: center;
  }

  .booking-text h2 {
    font-size: 36px;
  }

  .booking-cta {
    padding: 24px;
  }

  .booking-cta h3 {
    font-size: 24px;
  }

  .booking-buttons {
    flex-direction: column;
    gap: 12px;
  }

  .booking-image {
    height: 300px;
  }

  /* On mobile, show the inline image and hide the right-side image */
  .mobile-only {
    display: block;
  }
  .desktop-only {
    display: none;
  }

  /* Mobile Navigation */
  .nav-menu {
    display: none;
  }

  .mobile-menu-toggle {
    display: flex;
  }

  .logo img {
    height: 80px;
    max-width: 250px;
  }

  .floating-element-1,
  .floating-element-2 {
    display: none;
  }

  /* Better mobile spacing */
  .container {
    padding: 0 20px;
  }

  .section-header {
    margin-bottom: 50px;
  }

  .section-title {
    font-size: 36px;
  }

  .section-subtitle {
    font-size: 16px;
  }

  /* Mobile-centered content */
  .services-text,
  .about-text {
    text-align: center;
  }

  .services-text h2,
  .about-text h2 {
    font-size: 36px;
  }

  /* Mobile: center inner text/content for Services cards */
  .service-card {
    text-align: center;
  }
  .service-card h3,
  .service-card p,
  .service-features,
  .service-price {
    text-align: center;
  }
  .service-features li {
    justify-content: center;
  }
  /* Center the icon block above text on mobile */
  .service-icon {
    margin: 0 auto 24px;
  }

  /* Mobile services/contact section layout */
  .services-list {
    text-align: center; /* center list content on mobile */
    max-width: 320px;
    margin: 0 auto 30px;
  }

  .services-list li {
    margin-bottom: 14px;
    font-size: 15px;
    line-height: 1.4;
    justify-content: center; /* center icon + text row */
  }

  .services-list svg {
    width: 16px;
    height: 16px;
    margin-right: 10px;
  }
}

@media (max-width: 480px) {
  .hero {
    padding-top: 100px; /* Adjust for smallest mobile navbar */
  }

  .hero-text h1 {
    font-size: 42px;
    line-height: 1.1;
  }

  .hero-text p {
    font-size: 18px;
    margin-bottom: 32px;
  }

  /* Mobile dropdown positioning for small screens */
  .nav-menu.mobile-active {
    top: 100px; /* Match small mobile navbar height */
    padding: 12px;
  }

  .nav-menu.mobile-active .nav-link,
  .nav-menu.mobile-active .book-btn {
    margin-bottom: 3px;
    padding: 10px 16px;
  }

  /* Page header responsive padding for small screens */
  .page-header {
    padding: 110px 0 50px;
  }

  .hero-stats {
    flex-direction: column;
    gap: 20px;
    align-items: center;
  }

  .stat-item {
    text-align: center;
  }

  .hero-buttons {
    flex-direction: column;
    gap: 16px;
    width: 100%;
  }

  .btn-primary,
  .btn-secondary {
    width: 100%;
    text-align: center;
    padding: 16px 24px;
  }

  .hero-visual {
    height: 400px;
  }

  .hero-image-main {
    height: 350px;
  }

  .elements-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }

  .element-card {
    padding: 30px 20px;
  }

  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    text-align: center;
  }

  /* Better mobile typography */
  .section-title {
    font-size: 32px;
  }

  .services-text h2,
  .about-text h2 {
    font-size: 32px;
    line-height: 1.2;
  }

  .services-text p,
  .about-text p {
    font-size: 16px;
  }

  .services-image {
    height: 250px;
    margin-top: 20px;
  }

  /* Mobile navigation improvements */
  .logo img {
    height: 70px;
    max-width: 200px;
  }

  .navbar {
    padding: 12px 0;
  }

  /* Ensure proper mobile spacing */
  .container {
    padding: 0 15px;
  }

  .section-header {
    margin-bottom: 40px;
  }

  /* Mobile-specific element spacing */
  .element-card h3 {
    font-size: 20px;
  }

  .element-card p {
    font-size: 14px;
  }

  /* FAQ mobile improvements */
  .faq-item {
    padding: 24px 20px;
  }

  .faq-item h3 {
    font-size: 18px;
  }

  .faq-item p {
    font-size: 14px;
  }
}

/* Extra small devices (320px and below) */
@media (max-width: 320px) {
  .hero-text h1 {
    font-size: 36px;
  }

  .hero-text p {
    font-size: 16px;
  }

  .section-title {
    font-size: 28px;
  }

  .services-text h2,
  .about-text h2 {
    font-size: 28px;
  }

  /* Ensure vertical layout on small mobile */
  .services-content {
    display: grid !important;
    grid-template-columns: 1fr !important;
    gap: 30px;
  }

  /* Small mobile services/contact section */
  .services-list {
    max-width: 280px;
    text-align: center; /* keep centered on very small phones */
    margin-left: auto;
    margin-right: auto;
  }

  .services-list li {
    font-size: 14px;
    margin-bottom: 12px;
    justify-content: center;
  }

  .services-image {
    height: 200px;
  }

  .container {
    padding: 0 12px;
  }

  .logo img {
    height: 60px;
    max-width: 180px;
  }

  .btn-primary,
  .btn-secondary {
    padding: 14px 20px;
    font-size: 14px;
  }

  .element-card {
    padding: 24px 16px;
  }

  .faq-item {
    padding: 20px 16px;
  }

  /* Mobile booking section for small screens */
  .booking-text h2 {
    font-size: 28px;
  }

  .booking-cta {
    padding: 20px;
  }

  .booking-cta h3 {
    font-size: 20px;
  }

  .booking-image {
    height: 250px;
  }
}

/* Scroll to top button - mobile only */
.scroll-to-top {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 50px;
  height: 50px;
  background: var(--primary-pink);
  border: none;
  border-radius: 50%;
  color: var(--white);
  font-size: 18px;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: all 0.3s ease;
  z-index: 1000;
  display: none; /* Hidden by default */
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.scroll-to-top:hover {
  background: var(--charcoal);
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
}

/* Desktop-specific styles for service cards */
@media (min-width: 769px) {
  .service-card-link {
    height: 100%;
  }

  .service-card {
    height: 100%;
    min-height: 500px;
    text-align: center;
  }

  .service-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 24px auto;
  }

  .service-card h3,
  .service-card p,
  .service-features,
  .service-price {
    text-align: center;
  }

  .service-features {
    flex-grow: 1;
  }

  .service-features li {
    justify-content: center;
  }

  .service-price {
    margin-top: auto;
    text-align: center;
  }
}

/* Show scroll button only on mobile */
@media (max-width: 768px) {
  .scroll-to-top {
    display: block;
  }
}
